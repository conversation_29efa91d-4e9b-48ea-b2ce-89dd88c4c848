{"$schema": "https://designers.dev/schema.json", "version": "0.1.0", "name": "My Design System", "description": "Custom design system configuration", "theme": {"default": "light", "autoDetect": true, "storage": true, "themes": {"light": {"colors": {"primary": {"50": "#eff6ff", "100": "#dbeafe", "200": "#bfdbfe", "300": "#93c5fd", "400": "#60a5fa", "500": "#3b82f6", "600": "#2563eb", "700": "#1d4ed8", "800": "#1e40af", "900": "#1e3a8a", "950": "#172554"}, "secondary": {"50": "#f8fafc", "100": "#f1f5f9", "200": "#e2e8f0", "300": "#cbd5e1", "400": "#94a3b8", "500": "#64748b", "600": "#475569", "700": "#334155", "800": "#1e293b", "900": "#0f172a", "950": "#020617"}, "success": {"50": "#f0fdf4", "500": "#22c55e", "900": "#14532d"}, "warning": {"50": "#fffbeb", "500": "#f59e0b", "900": "#78350f"}, "error": {"50": "#fef2f2", "500": "#ef4444", "900": "#7f1d1d"}, "gray": {"50": "#f9fafb", "100": "#f3f4f6", "200": "#e5e7eb", "300": "#d1d5db", "400": "#9ca3af", "500": "#6b7280", "600": "#4b5563", "700": "#374151", "800": "#1f2937", "900": "#111827", "950": "#030712"}}, "semantic": {"text": {"primary": "#111827", "secondary": "#374151", "tertiary": "#6b7280", "inverse": "#ffffff", "disabled": "#9ca3af"}, "background": {"primary": "#ffffff", "secondary": "#f9fafb", "tertiary": "#f3f4f6", "inverse": "#111827", "overlay": "rgba(0, 0, 0, 0.5)"}, "border": {"primary": "#e5e7eb", "secondary": "#d1d5db", "tertiary": "#9ca3af", "focus": "#3b82f6", "error": "#ef4444", "success": "#22c55e"}, "interactive": {"primary": "#2563eb", "primaryHover": "#1d4ed8", "primaryActive": "#1e40af", "secondary": "#f3f4f6", "secondaryHover": "#e5e7eb", "secondaryActive": "#d1d5db"}}}, "dark": {"colors": {"primary": {"50": "#eff6ff", "500": "#3b82f6", "900": "#1e3a8a"}}, "semantic": {"text": {"primary": "#f3f4f6", "secondary": "#d1d5db", "tertiary": "#6b7280", "inverse": "#111827", "disabled": "#4b5563"}, "background": {"primary": "#111827", "secondary": "#1f2937", "tertiary": "#374151", "inverse": "#ffffff", "overlay": "rgba(0, 0, 0, 0.7)"}, "border": {"primary": "#374151", "secondary": "#4b5563", "tertiary": "#6b7280", "focus": "#60a5fa", "error": "#f87171", "success": "#4ade80"}, "interactive": {"primary": "#3b82f6", "primaryHover": "#60a5fa", "primaryActive": "#93c5fd", "secondary": "#374151", "secondaryHover": "#4b5563", "secondaryActive": "#6b7280"}}}}}, "typography": {"fontFamily": {"sans": ["Inter", "-apple-system", "BlinkMacSystemFont", "Segoe UI", "Roboto", "sans-serif"], "serif": ["Charter", "Bitstream Charter", "Sitka Text", "Cambria", "serif"], "mono": ["JetBrains Mono", "Fira Code", "Consolas", "Liberation Mono", "monospace"]}, "fontWeight": {"thin": 100, "light": 300, "normal": 400, "medium": 500, "semibold": 600, "bold": 700, "extrabold": 800, "black": 900}, "fontSize": {"xs": "0.75rem", "sm": "0.875rem", "base": "1rem", "lg": "1.125rem", "xl": "1.25rem", "2xl": "1.5rem", "3xl": "1.875rem", "4xl": "2.25rem", "5xl": "3rem", "6xl": "3.75rem"}, "lineHeight": {"none": 1, "tight": 1.25, "snug": 1.375, "normal": 1.5, "relaxed": 1.625, "loose": 2}, "letterSpacing": {"tighter": "-0.05em", "tight": "-0.025em", "normal": "0em", "wide": "0.025em", "wider": "0.05em", "widest": "0.1em"}, "scales": {"heading": {"h1": {"fontSize": "clamp(2.25rem, 4vw, 4.5rem)", "lineHeight": "1.1", "letterSpacing": "-0.025em", "fontWeight": 700}, "h2": {"fontSize": "clamp(1.875rem, 3.5vw, 3.75rem)", "lineHeight": "1.2", "letterSpacing": "-0.025em", "fontWeight": 600}, "h3": {"fontSize": "clamp(1.5rem, 3vw, 3rem)", "lineHeight": "1.25", "letterSpacing": "-0.02em", "fontWeight": 600}}, "body": {"large": {"fontSize": "1.125rem", "lineHeight": "1.6"}, "base": {"fontSize": "1rem", "lineHeight": "1.5"}, "small": {"fontSize": "0.875rem", "lineHeight": "1.4"}}}}, "spacing": {"scale": {"0": "0px", "px": "1px", "0.5": "0.125rem", "1": "0.25rem", "1.5": "0.375rem", "2": "0.5rem", "2.5": "0.625rem", "3": "0.75rem", "3.5": "0.875rem", "4": "1rem", "5": "1.25rem", "6": "1.5rem", "7": "1.75rem", "8": "2rem", "9": "2.25rem", "10": "2.5rem", "12": "3rem", "14": "3.5rem", "16": "4rem", "20": "5rem", "24": "6rem", "28": "7rem", "32": "8rem", "36": "9rem", "40": "10rem", "48": "12rem", "56": "14rem", "64": "16rem", "72": "18rem", "80": "20rem", "96": "24rem"}, "semantic": {"component": {"xs": "0.25rem", "sm": "0.5rem", "md": "1rem", "lg": "1.5rem", "xl": "2rem"}, "layout": {"xs": "1rem", "sm": "1.5rem", "md": "2rem", "lg": "3rem", "xl": "4rem", "2xl": "6rem", "3xl": "8rem"}}}, "responsive": {"autoDetect": true, "breakpoints": {"xs": {"min": "0px", "max": "639px", "cols": 4, "gutter": "1rem"}, "sm": {"min": "640px", "max": "767px", "cols": 8, "gutter": "1.5rem"}, "md": {"min": "768px", "max": "1023px", "cols": 8, "gutter": "2rem"}, "lg": {"min": "1024px", "max": "1279px", "cols": 12, "gutter": "2rem"}, "xl": {"min": "1280px", "max": "1535px", "cols": 12, "gutter": "2.5rem"}, "2xl": {"min": "1536px", "cols": 12, "gutter": "3rem"}}, "containerSizes": {"xs": "100%", "sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px"}}, "effects": {"shadows": {"none": "none", "xs": "0 1px 2px 0 rgba(0, 0, 0, 0.05)", "sm": "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)", "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)", "2xl": "0 25px 50px -12px rgba(0, 0, 0, 0.25)", "inner": "inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)"}, "borderRadius": {"none": "0", "xs": "0.125rem", "sm": "0.25rem", "md": "0.375rem", "lg": "0.5rem", "xl": "0.75rem", "2xl": "1rem", "3xl": "1.5rem", "full": "9999px"}, "gradients": {"primary": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", "secondary": "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)", "success": "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)", "warning": "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)", "error": "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)"}, "blur": {"none": "0", "xs": "2px", "sm": "4px", "md": "8px", "lg": "12px", "xl": "16px", "2xl": "24px", "3xl": "40px"}, "opacity": {"0": "0", "5": "0.05", "10": "0.1", "25": "0.25", "50": "0.5", "75": "0.75", "90": "0.9", "95": "0.95", "100": "1"}}, "tokens": {"prefix": "designers", "output": "./src/styles/tokens", "formats": ["css", "ts", "scss"], "include": ["colors", "typography", "spacing", "effects"], "exclude": [], "cssVariables": {"generateFor": ["light", "dark"], "selector": ":root", "mediaQueries": {"dark": "@media (prefers-color-scheme: dark)"}}}, "components": {"output": "./src/components/ui", "typescript": true, "styling": "css-in-js", "library": {"button": {"variants": {"primary": {"backgroundColor": "semantic.interactive.primary", "color": "semantic.text.inverse", "border": "none", "hover": {"backgroundColor": "semantic.interactive.primaryHover"}, "active": {"backgroundColor": "semantic.interactive.primaryActive"}}, "secondary": {"backgroundColor": "semantic.interactive.secondary", "color": "semantic.text.primary", "border": "1px solid semantic.border.primary", "hover": {"backgroundColor": "semantic.interactive.secondaryHover"}}, "outline": {"backgroundColor": "transparent", "color": "semantic.interactive.primary", "border": "1px solid semantic.interactive.primary", "hover": {"backgroundColor": "semantic.interactive.primary", "color": "semantic.text.inverse"}}, "ghost": {"backgroundColor": "transparent", "color": "semantic.text.primary", "border": "none", "hover": {"backgroundColor": "semantic.interactive.secondary"}}, "destructive": {"backgroundColor": "colors.error.500", "color": "semantic.text.inverse", "border": "none", "hover": {"backgroundColor": "colors.error.600"}}}, "sizes": {"xs": {"padding": "spacing.1 spacing.2", "fontSize": "typography.fontSize.xs", "borderRadius": "effects.borderRadius.sm"}, "sm": {"padding": "spacing.1 spacing.3", "fontSize": "typography.fontSize.sm", "borderRadius": "effects.borderRadius.md"}, "md": {"padding": "spacing.2 spacing.4", "fontSize": "typography.fontSize.base", "borderRadius": "effects.borderRadius.md"}, "lg": {"padding": "spacing.3 spacing.6", "fontSize": "typography.fontSize.lg", "borderRadius": "effects.borderRadius.lg"}, "xl": {"padding": "spacing.4 spacing.8", "fontSize": "typography.fontSize.xl", "borderRadius": "effects.borderRadius.lg"}}, "states": {"disabled": {"opacity": "effects.opacity.50", "cursor": "not-allowed"}, "loading": {"opacity": "effects.opacity.75", "cursor": "wait"}}}, "input": {"variants": {"default": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "1px solid semantic.border.primary", "focus": {"borderColor": "semantic.border.focus", "boxShadow": "0 0 0 3px rgba(59, 130, 246, 0.1)"}}, "error": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "1px solid semantic.border.error", "focus": {"borderColor": "semantic.border.error", "boxShadow": "0 0 0 3px rgba(239, 68, 68, 0.1)"}}, "success": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "1px solid semantic.border.success", "focus": {"borderColor": "semantic.border.success", "boxShadow": "0 0 0 3px rgba(34, 197, 94, 0.1)"}}}, "sizes": {"sm": {"padding": "spacing.2 spacing.3", "fontSize": "typography.fontSize.sm", "borderRadius": "effects.borderRadius.md"}, "md": {"padding": "spacing.2.5 spacing.3.5", "fontSize": "typography.fontSize.base", "borderRadius": "effects.borderRadius.md"}, "lg": {"padding": "spacing.3 spacing.4", "fontSize": "typography.fontSize.lg", "borderRadius": "effects.borderRadius.lg"}}}, "card": {"variants": {"default": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "1px solid semantic.border.primary", "borderRadius": "effects.borderRadius.lg"}, "elevated": {"backgroundColor": "semantic.background.primary", "color": "semantic.text.primary", "border": "none", "borderRadius": "effects.borderRadius.lg", "boxShadow": "effects.shadows.md"}, "outlined": {"backgroundColor": "transparent", "color": "semantic.text.primary", "border": "2px solid semantic.border.primary", "borderRadius": "effects.borderRadius.lg"}}, "sizes": {"sm": {"padding": "spacing.4"}, "md": {"padding": "spacing.6"}, "lg": {"padding": "spacing.8"}}}}}, "animations": {"enabled": true, "respectReducedMotion": true, "library": "framer-motion", "presets": {"fadeIn": {"duration": 300, "easing": "ease-out"}, "slideUp": {"duration": 400, "easing": "ease-out"}, "scale": {"duration": 200, "easing": "ease-in-out"}}}, "integrations": {"uiLibrary": null, "styling": "css", "bundler": "vite", "framework": "react", "storybook": {"enabled": false, "addons": ["@storybook/addon-docs"]}, "tailwind": {"enabled": false, "configPath": "./tailwind.config.js"}}, "build": {"watch": ["src/**/*.{ts,tsx,js,jsx}"], "output": {"tokens": "./dist/tokens", "components": "./dist/components"}, "optimization": {"treeshaking": true, "minification": true, "sourcemaps": true}}, "development": {"hotReload": true, "devtools": true, "playground": {"enabled": true, "port": 3001}}, "accessibility": {"enforceContrast": true, "minimumContrastRatio": 4.5, "focusVisible": true, "reducedMotion": "respect"}, "performance": {"lazyLoading": true, "bundleSize": {"maxSize": "50kb", "warn": "30kb"}, "caching": {"tokens": true, "components": false}}}