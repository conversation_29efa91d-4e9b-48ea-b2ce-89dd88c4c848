# Designers - Headless Design System

A lightweight, headless design system for React applications. Zero-component system that provides structured design tokens, adaptive layouts, and optional animation systems while remaining UI-library agnostic.

## 🚀 Features

- **🎨 Comprehensive Design Tokens**: Colors, typography, spacing, shadows, and more
- **📱 Responsive by Default**: Mobile-first breakpoints with auto-detection
- **🌙 Dark Mode Support**: Automatic system theme detection
- **⚡ Performance Focused**: Tree-shakeable, minimal runtime overhead
- **🔧 TypeScript Native**: Full type safety and IntelliSense support
- **🎯 Framework Agnostic**: Works with any UI library or custom components
- **🪝 React Hooks**: Powerful hooks for responsive design and theming

## 📦 Packages

This is a monorepo containing multiple packages:

- **`@designers/core`** - Core design tokens and utilities
- **`@designers/react`** - React hooks and providers
- **`@designers/animations`** - Animation primitives and utilities
- **`@designers/cli`** - Command-line tools for code generation

## 🏗️ Installation

```bash
npm install @designers/core @designers/react
```

## 🎯 Quick Start

### 1. Setup the Provider

```tsx
import { DesignSystemProvider } from '@designers/react';

function App() {
  return (
    <DesignSystemProvider
      config={{
        theme: 'light', // or 'dark'
        autoDetectColorScheme: true,
        autoDetectBreakpoint: true,
      }}
    >
      <YourApp />
    </DesignSystemProvider>
  );
}
```

### 2. Use Design Tokens

```tsx
import { useDesignTokens } from '@designers/react';

function MyComponent() {
  const { colors, spacing, typography } = useDesignTokens();
  
  return (
    <div
      style={{
        backgroundColor: colors.semantic.background.primary,
        color: colors.semantic.text.primary,
        padding: spacing[4],
        fontFamily: typography.fontFamily.sans.join(', '),
      }}
    >
      Hello, Designers!
    </div>
  );
}
```

### 3. Responsive Design

```tsx
import { useResponsive } from '@designers/react';

function ResponsiveComponent() {
  const { state, resolve } = useResponsive();
  
  const padding = resolve({
    xs: '1rem',
    md: '2rem',
    lg: '3rem',
  });
  
  return (
    <div style={{ padding }}>
      Current breakpoint: {state.breakpoint}
      {state.isMobile && <p>Mobile view</p>}
      {state.isDesktop && <p>Desktop view</p>}
    </div>
  );
}
```

### 4. Color Scheme Management

```tsx
import { useColorScheme } from '@designers/react';

function ThemeToggle() {
  const { theme, toggleTheme, isDark } = useColorScheme();
  
  return (
    <button onClick={toggleTheme}>
      Switch to {isDark ? 'light' : 'dark'} mode
    </button>
  );
}
```

## 🎨 Design Tokens

### Colors

```tsx
const { colors, semantic } = useDesignTokens();

// Base colors
colors.primary[500]  // #3b82f6
colors.gray[100]     // #f3f4f6

// Semantic colors (theme-aware)
semantic.text.primary      // Adapts to light/dark mode
semantic.background.primary
semantic.border.primary
```

### Typography

```tsx
const { typography } = useDesignTokens();

// Font families
typography.fontFamily.sans  // ['Inter', 'system-ui', ...]
typography.fontFamily.mono  // ['JetBrains Mono', 'monospace', ...]

// Semantic scales
typography.heading.h1  // { fontSize: 'clamp(...)', lineHeight: '1.1', ... }
typography.body.base   // { fontSize: '1rem', lineHeight: '1.5' }
```

### Spacing

```tsx
const { spacing, getSpacing } = useDesignTokens();

spacing[4]        // '1rem' (16px)
spacing[8]        // '2rem' (32px)
getSpacing(12)    // '3rem' (48px)
```

### Responsive Breakpoints

```tsx
const { breakpoint, isBreakpointUp } = useResponsive();

breakpoint                    // 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'
isBreakpointUp('md')         // true if current breakpoint is md or larger
```

## 🔧 Advanced Usage

### Custom Theme Extension

```tsx
import { deepMerge, colorThemes } from '@designers/core';

const customTheme = deepMerge(colorThemes.light, {
  colors: {
    primary: {
      500: '#your-brand-color',
    },
  },
});
```

### CSS Custom Properties

```tsx
import { generateCSSCustomProperties } from '@designers/core';

// Generate CSS variables
const cssVars = generateCSSCustomProperties('dark');
// Outputs: :root { --designers-color-primary-500: #3b82f6; ... }
```

### Media Queries

```tsx
import { useMediaQuery } from '@designers/react';

function Component() {
  const isMobile = useMediaQuery('(max-width: 768px)');
  const prefersReducedMotion = useMediaQuery('(prefers-reduced-motion: reduce)');
  
  return (
    <div>
      {isMobile ? 'Mobile' : 'Desktop'}
      {prefersReducedMotion && <p>Reduced motion enabled</p>}
    </div>
  );
}
```

## 🎭 UI Library Integration

Designers works seamlessly with popular UI libraries:

### shadcn/ui

```tsx
import { useDesignTokens } from '@designers/react';
import { Button } from '@/components/ui/button';

function ThemedButton() {
  const { colors } = useDesignTokens();
  
  return (
    <Button 
      style={{ 
        '--primary': colors.primary[600],
        '--primary-foreground': colors.white,
      }}
    >
      Themed Button
    </Button>
  );
}
```

### Chakra UI

```tsx
import { useDesignTokens } from '@designers/react';
import { extendTheme, ChakraProvider } from '@chakra-ui/react';

function App() {
  const { colors, spacing } = useDesignTokens();
  
  const chakraTheme = extendTheme({
    colors: {
      primary: colors.primary,
      gray: colors.gray,
    },
    space: spacing,
  });
  
  return (
    <ChakraProvider theme={chakraTheme}>
      <YourApp />
    </ChakraProvider>
  );
}
```

## 📱 Responsive Design Patterns

### Container Queries

```tsx
import { useResponsive } from '@designers/react';

function ResponsiveCard() {
  const { resolve } = useResponsive();
  
  const layout = resolve({
    xs: 'vertical',
    md: 'horizontal',
  });
  
  return (
    <div className={`card card--${layout}`}>
      {/* Content adapts based on container size */}
    </div>
  );
}
```

### Fluid Typography

```tsx
import { useDesignTokens } from '@designers/react';

function FluidText() {
  const { typography } = useDesignTokens();
  
  return (
    <h1 style={typography.heading.h1}>
      {/* Font size scales smoothly between breakpoints */}
      Fluid Heading
    </h1>
  );
}
```

## 🎨 Animation Integration

```tsx
import { usePrefersReducedMotion } from '@designers/react';
import { motion } from 'framer-motion';

function AnimatedComponent() {
  const prefersReducedMotion = usePrefersReducedMotion();
  
  return (
    <motion.div
      animate={{ opacity: 1 }}
      transition={{ 
        duration: prefersReducedMotion ? 0 : 0.3 
      }}
    >
      Respects user motion preferences
    </motion.div>
  );
}
```

## 🛠️ Development

```bash
# Install dependencies
npm install

# Build all packages
npm run build

# Run in development mode
npm run dev

# Run tests
npm run test

# Lint code
npm run lint
```

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.

## 🤝 Contributing

Contributions are welcome! Please read our [Contributing Guide](CONTRIBUTING.md) for details.

---

Built with ❤️ by the Designers team
